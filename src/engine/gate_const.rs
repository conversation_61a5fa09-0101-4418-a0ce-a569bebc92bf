pub static GATE_BASE_ASSETS: &[&str] = &[
    "KAITO",
    "SUSH<PERSON>",
    "USUAL",
    "THETA",
    "UMA",
];
pub const CURRENCY_LEN: usize = 5;
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Hash)]
pub enum GateBaseAssetCurrency {
    KAITO,
    SUSHI,
    USUAL,
    THETA,
    UMA,
}
impl GateBaseAssetCurrency {
    pub fn to_str(&self) -> &'static str {
        match self {
            GateBaseAssetCurrency::KAITO => "KAITO",
            GateBaseAssetCurrency::SUSHI => "SUSHI",
            GateBaseAssetCurrency::USUAL => "USUAL",
            GateBaseAssetCurrency::THETA => "THETA",
            GateBaseAssetCurrency::UMA => "UMA",
        }
    }
    pub fn from_symbol(symbol: &str) -> Option<Self> {
        let upper_symbol = symbol.to_uppercase();
        let symbol = upper_symbol.strip_suffix("USDT").unwrap_or(symbol).strip_suffix("_").unwrap_or(symbol);
        match symbol {
            "KAITO" => Some(GateBaseAssetCurrency::KAITO),
            "SUSHI" => Some(GateBaseAssetCurrency::SUSHI),
            "USUAL" => Some(GateBaseAssetCurrency::USUAL),
            "THETA" => Some(GateBaseAssetCurrency::THETA),
            "UMA" => Some(GateBaseAssetCurrency::UMA),
            _ => None,
        }
    }
    pub fn to_bn_symbol(&self) -> &'static str {
        match self {
            GateBaseAssetCurrency::KAITO => "KAITOUSDT",
            GateBaseAssetCurrency::SUSHI => "SUSHIUSDT",
            GateBaseAssetCurrency::USUAL => "USUALUSDT",
            GateBaseAssetCurrency::THETA => "THETAUSDT",
            GateBaseAssetCurrency::UMA => "UMAUSDT",
        }
    }
    pub fn to_gate_symbol(&self) -> &'static str {
        match self {
            GateBaseAssetCurrency::KAITO => "KAITO_USDT",
            GateBaseAssetCurrency::SUSHI => "SUSHI_USDT",
            GateBaseAssetCurrency::USUAL => "USUAL_USDT",
            GateBaseAssetCurrency::THETA => "THETA_USDT",
            GateBaseAssetCurrency::UMA => "UMA_USDT",
        }
    }
    pub fn from_usize(num: usize) -> Option<Self> {
        match num {
            0 => Some(GateBaseAssetCurrency::KAITO),
            1 => Some(GateBaseAssetCurrency::SUSHI),
            2 => Some(GateBaseAssetCurrency::USUAL),
            3 => Some(GateBaseAssetCurrency::THETA),
            4 => Some(GateBaseAssetCurrency::UMA),
            _ => None,
        }
    }
}
// bn and gate bbo map
 //[
//(bn_best_bid_price, bn_best_bid_qty, bn_best_ask_price, bn_best_ask_qty), 
//(gate_best_bid_price, gate_best_bid_qty, gate_best_ask_price, gate_best_ask_qty)]
pub static mut BN_AND_GATE_BBO_MAP: [([f64; 4], [f64; 4]); 5] = [([0.0, 0.0, 0.0, 0.0], [0.0, 0.0, 0.0, 0.0]); 5];
// req id, order id, order price, order status(0 for pending, 1 for ack
pub static mut GATE_PENDING_ORDERS: [(u64, u64, f64, u8); 5] = [(0, 0, 0.0, 0); 5];
pub static GATE_PRICE_TICKS: [f64; 5] = [
    0.0001f64, // KAITO
    0.0001f64, // SUSHI
    0.00001f64, // USUAL
    0.0001f64, // THETA
    0.0001f64, // UMA
];
