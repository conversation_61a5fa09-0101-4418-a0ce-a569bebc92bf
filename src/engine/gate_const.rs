pub static GATE_BASE_ASSETS: &[&str] = &[
    "SOL",
    "LIN<PERSON>",
    "<PERSON><PERSON>",
    "AVAX",
    "IP",
    "FARTCOIN",
    "LRC",
    "BIO",
    "ARB",
    "WIF",
    "DOT",
    "UNI",
    "FORM",
    "OP",
    "WLD",
    "QTUM",
    "NEAR",
    "LDO",
    "ONDO",
    "TIA",
    "ZRX",
    "RLC",
    "BERA",
    "PENDLE",
    "W",
    "API3",
    "FET",
    "JUP",
    "ATOM",
    "MYX",
    "NEO",
    "OM",
    "JOE",
    "SAND",
    "EIGEN",
    "RED",
    "AI",
    "MAGIC",
    "AERO",
    "MELANIA",
    "RENDER",
    "JTO",
    "ONT",
    "CELO",
    "APE",
    "S",
    "ALPINE",
    "KAITO",
    "SUSHI",
    "ARKM",
    "UMA",
    "ZRO",
    "HOOK",
    "XTZ",
    "<PERSON><PERSON><PERSON>",
    "ME<PERSON>",
    "STR<PERSON>",
    "STX",
    "ZKJ",
    "ALT",
    "THETA",
    "GMT",
    "CATI",
    "IOTA",
    "ASR",
    "CTSI",
    "FIS",
    "USDC",
    "OGN",
    "IO",
    "SOMI",
    "MORPHO",
    "EPIC",
    "ID",
    "KAVA",
    "HFT",
    "SAGA",
    "JASMY",
    "LAYER",
    "HEI",
];
pub const CURRENCY_LEN: usize = 80;
pub enum GateBaseAssetCurrency {
    SOL,
    LINK,
    SUI,
    AVAX,
    IP,
    FARTCOIN,
    LRC,
    BIO,
    ARB,
    WIF,
    DOT,
    UNI,
    FORM,
    OP,
    WLD,
    QTUM,
    NEAR,
    LDO,
    ONDO,
    TIA,
    ZRX,
    RLC,
    BERA,
    PENDLE,
    W,
    API3,
    FET,
    JUP,
    ATOM,
    MYX,
    NEO,
    OM,
    JOE,
    SAND,
    EIGEN,
    RED,
    AI,
    MAGIC,
    AERO,
    MELANIA,
    RENDER,
    JTO,
    ONT,
    CELO,
    APE,
    S,
    ALPINE,
    KAITO,
    SUSHI,
    ARKM,
    UMA,
    ZRO,
    HOOK,
    XTZ,
    USUAL,
    MERL,
    STRK,
    STX,
    ZKJ,
    ALT,
    THETA,
    GMT,
    CATI,
    IOTA,
    ASR,
    CTSI,
    FIS,
    USDC,
    OGN,
    IO,
    SOMI,
    MORPHO,
    EPIC,
    ID,
    KAVA,
    HFT,
    SAGA,
    JASMY,
    LAYER,
    HEI,
}
impl GateBaseAssetCurrency {
    pub fn to_str(&self) -> &'static str {
        match self {
            GateBaseAssetCurrency::SOL => "SOL",
            GateBaseAssetCurrency::LINK => "LINK",
            GateBaseAssetCurrency::SUI => "SUI",
            GateBaseAssetCurrency::AVAX => "AVAX",
            GateBaseAssetCurrency::IP => "IP",
            GateBaseAssetCurrency::FARTCOIN => "FARTCOIN",
            GateBaseAssetCurrency::LRC => "LRC",
            GateBaseAssetCurrency::BIO => "BIO",
            GateBaseAssetCurrency::ARB => "ARB",
            GateBaseAssetCurrency::WIF => "WIF",
            GateBaseAssetCurrency::DOT => "DOT",
            GateBaseAssetCurrency::UNI => "UNI",
            GateBaseAssetCurrency::FORM => "FORM",
            GateBaseAssetCurrency::OP => "OP",
            GateBaseAssetCurrency::WLD => "WLD",
            GateBaseAssetCurrency::QTUM => "QTUM",
            GateBaseAssetCurrency::NEAR => "NEAR",
            GateBaseAssetCurrency::LDO => "LDO",
            GateBaseAssetCurrency::ONDO => "ONDO",
            GateBaseAssetCurrency::TIA => "TIA",
            GateBaseAssetCurrency::ZRX => "ZRX",
            GateBaseAssetCurrency::RLC => "RLC",
            GateBaseAssetCurrency::BERA => "BERA",
            GateBaseAssetCurrency::PENDLE => "PENDLE",
            GateBaseAssetCurrency::W => "W",
            GateBaseAssetCurrency::API3 => "API3",
            GateBaseAssetCurrency::FET => "FET",
            GateBaseAssetCurrency::JUP => "JUP",
            GateBaseAssetCurrency::ATOM => "ATOM",
            GateBaseAssetCurrency::MYX => "MYX",
            GateBaseAssetCurrency::NEO => "NEO",
            GateBaseAssetCurrency::OM => "OM",
            GateBaseAssetCurrency::JOE => "JOE",
            GateBaseAssetCurrency::SAND => "SAND",
            GateBaseAssetCurrency::EIGEN => "EIGEN",
            GateBaseAssetCurrency::RED => "RED",
            GateBaseAssetCurrency::AI => "AI",
            GateBaseAssetCurrency::MAGIC => "MAGIC",
            GateBaseAssetCurrency::AERO => "AERO",
            GateBaseAssetCurrency::MELANIA => "MELANIA",
            GateBaseAssetCurrency::RENDER => "RENDER",
            GateBaseAssetCurrency::JTO => "JTO",
            GateBaseAssetCurrency::ONT => "ONT",
            GateBaseAssetCurrency::CELO => "CELO",
            GateBaseAssetCurrency::APE => "APE",
            GateBaseAssetCurrency::S => "S",
            GateBaseAssetCurrency::ALPINE => "ALPINE",
            GateBaseAssetCurrency::KAITO => "KAITO",
            GateBaseAssetCurrency::SUSHI => "SUSHI",
            GateBaseAssetCurrency::ARKM => "ARKM",
            GateBaseAssetCurrency::UMA => "UMA",
            GateBaseAssetCurrency::ZRO => "ZRO",
            GateBaseAssetCurrency::HOOK => "HOOK",
            GateBaseAssetCurrency::XTZ => "XTZ",
            GateBaseAssetCurrency::USUAL => "USUAL",
            GateBaseAssetCurrency::MERL => "MERL",
            GateBaseAssetCurrency::STRK => "STRK",
            GateBaseAssetCurrency::STX => "STX",
            GateBaseAssetCurrency::ZKJ => "ZKJ",
            GateBaseAssetCurrency::ALT => "ALT",
            GateBaseAssetCurrency::THETA => "THETA",
            GateBaseAssetCurrency::GMT => "GMT",
            GateBaseAssetCurrency::CATI => "CATI",
            GateBaseAssetCurrency::IOTA => "IOTA",
            GateBaseAssetCurrency::ASR => "ASR",
            GateBaseAssetCurrency::CTSI => "CTSI",
            GateBaseAssetCurrency::FIS => "FIS",
            GateBaseAssetCurrency::USDC => "USDC",
            GateBaseAssetCurrency::OGN => "OGN",
            GateBaseAssetCurrency::IO => "IO",
            GateBaseAssetCurrency::SOMI => "SOMI",
            GateBaseAssetCurrency::MORPHO => "MORPHO",
            GateBaseAssetCurrency::EPIC => "EPIC",
            GateBaseAssetCurrency::ID => "ID",
            GateBaseAssetCurrency::KAVA => "KAVA",
            GateBaseAssetCurrency::HFT => "HFT",
            GateBaseAssetCurrency::SAGA => "SAGA",
            GateBaseAssetCurrency::JASMY => "JASMY",
            GateBaseAssetCurrency::LAYER => "LAYER",
            GateBaseAssetCurrency::HEI => "HEI",
        }
    }
    pub fn from_symbol(symbol: &str) -> Option<Self> {
        let upper_symbol = symbol.to_uppercase();
        let symbol = upper_symbol.strip_suffix("USDT").unwrap_or(symbol).strip_suffix("_").unwrap_or(symbol);
        match symbol {
            "SOL" => Some(GateBaseAssetCurrency::SOL),
            "LINK" => Some(GateBaseAssetCurrency::LINK),
            "SUI" => Some(GateBaseAssetCurrency::SUI),
            "AVAX" => Some(GateBaseAssetCurrency::AVAX),
            "IP" => Some(GateBaseAssetCurrency::IP),
            "FARTCOIN" => Some(GateBaseAssetCurrency::FARTCOIN),
            "LRC" => Some(GateBaseAssetCurrency::LRC),
            "BIO" => Some(GateBaseAssetCurrency::BIO),
            "ARB" => Some(GateBaseAssetCurrency::ARB),
            "WIF" => Some(GateBaseAssetCurrency::WIF),
            "DOT" => Some(GateBaseAssetCurrency::DOT),
            "UNI" => Some(GateBaseAssetCurrency::UNI),
            "FORM" => Some(GateBaseAssetCurrency::FORM),
            "OP" => Some(GateBaseAssetCurrency::OP),
            "WLD" => Some(GateBaseAssetCurrency::WLD),
            "QTUM" => Some(GateBaseAssetCurrency::QTUM),
            "NEAR" => Some(GateBaseAssetCurrency::NEAR),
            "LDO" => Some(GateBaseAssetCurrency::LDO),
            "ONDO" => Some(GateBaseAssetCurrency::ONDO),
            "TIA" => Some(GateBaseAssetCurrency::TIA),
            "ZRX" => Some(GateBaseAssetCurrency::ZRX),
            "RLC" => Some(GateBaseAssetCurrency::RLC),
            "BERA" => Some(GateBaseAssetCurrency::BERA),
            "PENDLE" => Some(GateBaseAssetCurrency::PENDLE),
            "W" => Some(GateBaseAssetCurrency::W),
            "API3" => Some(GateBaseAssetCurrency::API3),
            "FET" => Some(GateBaseAssetCurrency::FET),
            "JUP" => Some(GateBaseAssetCurrency::JUP),
            "ATOM" => Some(GateBaseAssetCurrency::ATOM),
            "MYX" => Some(GateBaseAssetCurrency::MYX),
            "NEO" => Some(GateBaseAssetCurrency::NEO),
            "OM" => Some(GateBaseAssetCurrency::OM),
            "JOE" => Some(GateBaseAssetCurrency::JOE),
            "SAND" => Some(GateBaseAssetCurrency::SAND),
            "EIGEN" => Some(GateBaseAssetCurrency::EIGEN),
            "RED" => Some(GateBaseAssetCurrency::RED),
            "AI" => Some(GateBaseAssetCurrency::AI),
            "MAGIC" => Some(GateBaseAssetCurrency::MAGIC),
            "AERO" => Some(GateBaseAssetCurrency::AERO),
            "MELANIA" => Some(GateBaseAssetCurrency::MELANIA),
            "RENDER" => Some(GateBaseAssetCurrency::RENDER),
            "JTO" => Some(GateBaseAssetCurrency::JTO),
            "ONT" => Some(GateBaseAssetCurrency::ONT),
            "CELO" => Some(GateBaseAssetCurrency::CELO),
            "APE" => Some(GateBaseAssetCurrency::APE),
            "S" => Some(GateBaseAssetCurrency::S),
            "ALPINE" => Some(GateBaseAssetCurrency::ALPINE),
            "KAITO" => Some(GateBaseAssetCurrency::KAITO),
            "SUSHI" => Some(GateBaseAssetCurrency::SUSHI),
            "ARKM" => Some(GateBaseAssetCurrency::ARKM),
            "UMA" => Some(GateBaseAssetCurrency::UMA),
            "ZRO" => Some(GateBaseAssetCurrency::ZRO),
            "HOOK" => Some(GateBaseAssetCurrency::HOOK),
            "XTZ" => Some(GateBaseAssetCurrency::XTZ),
            "USUAL" => Some(GateBaseAssetCurrency::USUAL),
            "MERL" => Some(GateBaseAssetCurrency::MERL),
            "STRK" => Some(GateBaseAssetCurrency::STRK),
            "STX" => Some(GateBaseAssetCurrency::STX),
            "ZKJ" => Some(GateBaseAssetCurrency::ZKJ),
            "ALT" => Some(GateBaseAssetCurrency::ALT),
            "THETA" => Some(GateBaseAssetCurrency::THETA),
            "GMT" => Some(GateBaseAssetCurrency::GMT),
            "CATI" => Some(GateBaseAssetCurrency::CATI),
            "IOTA" => Some(GateBaseAssetCurrency::IOTA),
            "ASR" => Some(GateBaseAssetCurrency::ASR),
            "CTSI" => Some(GateBaseAssetCurrency::CTSI),
            "FIS" => Some(GateBaseAssetCurrency::FIS),
            "USDC" => Some(GateBaseAssetCurrency::USDC),
            "OGN" => Some(GateBaseAssetCurrency::OGN),
            "IO" => Some(GateBaseAssetCurrency::IO),
            "SOMI" => Some(GateBaseAssetCurrency::SOMI),
            "MORPHO" => Some(GateBaseAssetCurrency::MORPHO),
            "EPIC" => Some(GateBaseAssetCurrency::EPIC),
            "ID" => Some(GateBaseAssetCurrency::ID),
            "KAVA" => Some(GateBaseAssetCurrency::KAVA),
            "HFT" => Some(GateBaseAssetCurrency::HFT),
            "SAGA" => Some(GateBaseAssetCurrency::SAGA),
            "JASMY" => Some(GateBaseAssetCurrency::JASMY),
            "LAYER" => Some(GateBaseAssetCurrency::LAYER),
            "HEI" => Some(GateBaseAssetCurrency::HEI),
            _ => None,
        }
    }
    pub fn to_bn_symbol(&self) -> &'static str {
        match self {
            GateBaseAssetCurrency::SOL => "SOLUSDT",
            GateBaseAssetCurrency::LINK => "LINKUSDT",
            GateBaseAssetCurrency::SUI => "SUIUSDT",
            GateBaseAssetCurrency::AVAX => "AVAXUSDT",
            GateBaseAssetCurrency::IP => "IPUSDT",
            GateBaseAssetCurrency::FARTCOIN => "FARTCOINUSDT",
            GateBaseAssetCurrency::LRC => "LRCUSDT",
            GateBaseAssetCurrency::BIO => "BIOUSDT",
            GateBaseAssetCurrency::ARB => "ARBUSDT",
            GateBaseAssetCurrency::WIF => "WIFUSDT",
            GateBaseAssetCurrency::DOT => "DOTUSDT",
            GateBaseAssetCurrency::UNI => "UNIUSDT",
            GateBaseAssetCurrency::FORM => "FORMUSDT",
            GateBaseAssetCurrency::OP => "OPUSDT",
            GateBaseAssetCurrency::WLD => "WLDUSDT",
            GateBaseAssetCurrency::QTUM => "QTUMUSDT",
            GateBaseAssetCurrency::NEAR => "NEARUSDT",
            GateBaseAssetCurrency::LDO => "LDOUSDT",
            GateBaseAssetCurrency::ONDO => "ONDOUSDT",
            GateBaseAssetCurrency::TIA => "TIAUSDT",
            GateBaseAssetCurrency::ZRX => "ZRXUSDT",
            GateBaseAssetCurrency::RLC => "RLCUSDT",
            GateBaseAssetCurrency::BERA => "BERAUSDT",
            GateBaseAssetCurrency::PENDLE => "PENDLEUSDT",
            GateBaseAssetCurrency::W => "WUSDT",
            GateBaseAssetCurrency::API3 => "API3USDT",
            GateBaseAssetCurrency::FET => "FETUSDT",
            GateBaseAssetCurrency::JUP => "JUPUSDT",
            GateBaseAssetCurrency::ATOM => "ATOMUSDT",
            GateBaseAssetCurrency::MYX => "MYXUSDT",
            GateBaseAssetCurrency::NEO => "NEOUSDT",
            GateBaseAssetCurrency::OM => "OMUSDT",
            GateBaseAssetCurrency::JOE => "JOEUSDT",
            GateBaseAssetCurrency::SAND => "SANDUSDT",
            GateBaseAssetCurrency::EIGEN => "EIGENUSDT",
            GateBaseAssetCurrency::RED => "REDUSDT",
            GateBaseAssetCurrency::AI => "AIUSDT",
            GateBaseAssetCurrency::MAGIC => "MAGICUSDT",
            GateBaseAssetCurrency::AERO => "AEROUSDT",
            GateBaseAssetCurrency::MELANIA => "MELANIAUSDT",
            GateBaseAssetCurrency::RENDER => "RENDERUSDT",
            GateBaseAssetCurrency::JTO => "JTOUSDT",
            GateBaseAssetCurrency::ONT => "ONTUSDT",
            GateBaseAssetCurrency::CELO => "CELOUSDT",
            GateBaseAssetCurrency::APE => "APEUSDT",
            GateBaseAssetCurrency::S => "SUSDT",
            GateBaseAssetCurrency::ALPINE => "ALPINEUSDT",
            GateBaseAssetCurrency::KAITO => "KAITOUSDT",
            GateBaseAssetCurrency::SUSHI => "SUSHIUSDT",
            GateBaseAssetCurrency::ARKM => "ARKMUSDT",
            GateBaseAssetCurrency::UMA => "UMAUSDT",
            GateBaseAssetCurrency::ZRO => "ZROUSDT",
            GateBaseAssetCurrency::HOOK => "HOOKUSDT",
            GateBaseAssetCurrency::XTZ => "XTZUSDT",
            GateBaseAssetCurrency::USUAL => "USUALUSDT",
            GateBaseAssetCurrency::MERL => "MERLUSDT",
            GateBaseAssetCurrency::STRK => "STRKUSDT",
            GateBaseAssetCurrency::STX => "STXUSDT",
            GateBaseAssetCurrency::ZKJ => "ZKJUSDT",
            GateBaseAssetCurrency::ALT => "ALTUSDT",
            GateBaseAssetCurrency::THETA => "THETAUSDT",
            GateBaseAssetCurrency::GMT => "GMTUSDT",
            GateBaseAssetCurrency::CATI => "CATIUSDT",
            GateBaseAssetCurrency::IOTA => "IOTAUSDT",
            GateBaseAssetCurrency::ASR => "ASRUSDT",
            GateBaseAssetCurrency::CTSI => "CTSIUSDT",
            GateBaseAssetCurrency::FIS => "FISUSDT",
            GateBaseAssetCurrency::USDC => "USDCUSDT",
            GateBaseAssetCurrency::OGN => "OGNUSDT",
            GateBaseAssetCurrency::IO => "IOUSDT",
            GateBaseAssetCurrency::SOMI => "SOMIUSDT",
            GateBaseAssetCurrency::MORPHO => "MORPHOUSDT",
            GateBaseAssetCurrency::EPIC => "EPICUSDT",
            GateBaseAssetCurrency::ID => "IDUSDT",
            GateBaseAssetCurrency::KAVA => "KAVAUSDT",
            GateBaseAssetCurrency::HFT => "HFTUSDT",
            GateBaseAssetCurrency::SAGA => "SAGAUSDT",
            GateBaseAssetCurrency::JASMY => "JASMYUSDT",
            GateBaseAssetCurrency::LAYER => "LAYERUSDT",
            GateBaseAssetCurrency::HEI => "HEIUSDT",
        }
    }
    pub fn to_gate_symbol(&self) -> &'static str {
        match self {
            GateBaseAssetCurrency::SOL => "SOL_USDT",
            GateBaseAssetCurrency::LINK => "LINK_USDT",
            GateBaseAssetCurrency::SUI => "SUI_USDT",
            GateBaseAssetCurrency::AVAX => "AVAX_USDT",
            GateBaseAssetCurrency::IP => "IP_USDT",
            GateBaseAssetCurrency::FARTCOIN => "FARTCOIN_USDT",
            GateBaseAssetCurrency::LRC => "LRC_USDT",
            GateBaseAssetCurrency::BIO => "BIO_USDT",
            GateBaseAssetCurrency::ARB => "ARB_USDT",
            GateBaseAssetCurrency::WIF => "WIF_USDT",
            GateBaseAssetCurrency::DOT => "DOT_USDT",
            GateBaseAssetCurrency::UNI => "UNI_USDT",
            GateBaseAssetCurrency::FORM => "FORM_USDT",
            GateBaseAssetCurrency::OP => "OP_USDT",
            GateBaseAssetCurrency::WLD => "WLD_USDT",
            GateBaseAssetCurrency::QTUM => "QTUM_USDT",
            GateBaseAssetCurrency::NEAR => "NEAR_USDT",
            GateBaseAssetCurrency::LDO => "LDO_USDT",
            GateBaseAssetCurrency::ONDO => "ONDO_USDT",
            GateBaseAssetCurrency::TIA => "TIA_USDT",
            GateBaseAssetCurrency::ZRX => "ZRX_USDT",
            GateBaseAssetCurrency::RLC => "RLC_USDT",
            GateBaseAssetCurrency::BERA => "BERA_USDT",
            GateBaseAssetCurrency::PENDLE => "PENDLE_USDT",
            GateBaseAssetCurrency::W => "W_USDT",
            GateBaseAssetCurrency::API3 => "API3_USDT",
            GateBaseAssetCurrency::FET => "FET_USDT",
            GateBaseAssetCurrency::JUP => "JUP_USDT",
            GateBaseAssetCurrency::ATOM => "ATOM_USDT",
            GateBaseAssetCurrency::MYX => "MYX_USDT",
            GateBaseAssetCurrency::NEO => "NEO_USDT",
            GateBaseAssetCurrency::OM => "OM_USDT",
            GateBaseAssetCurrency::JOE => "JOE_USDT",
            GateBaseAssetCurrency::SAND => "SAND_USDT",
            GateBaseAssetCurrency::EIGEN => "EIGEN_USDT",
            GateBaseAssetCurrency::RED => "RED_USDT",
            GateBaseAssetCurrency::AI => "AI_USDT",
            GateBaseAssetCurrency::MAGIC => "MAGIC_USDT",
            GateBaseAssetCurrency::AERO => "AERO_USDT",
            GateBaseAssetCurrency::MELANIA => "MELANIA_USDT",
            GateBaseAssetCurrency::RENDER => "RENDER_USDT",
            GateBaseAssetCurrency::JTO => "JTO_USDT",
            GateBaseAssetCurrency::ONT => "ONT_USDT",
            GateBaseAssetCurrency::CELO => "CELO_USDT",
            GateBaseAssetCurrency::APE => "APE_USDT",
            GateBaseAssetCurrency::S => "S_USDT",
            GateBaseAssetCurrency::ALPINE => "ALPINE_USDT",
            GateBaseAssetCurrency::KAITO => "KAITO_USDT",
            GateBaseAssetCurrency::SUSHI => "SUSHI_USDT",
            GateBaseAssetCurrency::ARKM => "ARKM_USDT",
            GateBaseAssetCurrency::UMA => "UMA_USDT",
            GateBaseAssetCurrency::ZRO => "ZRO_USDT",
            GateBaseAssetCurrency::HOOK => "HOOK_USDT",
            GateBaseAssetCurrency::XTZ => "XTZ_USDT",
            GateBaseAssetCurrency::USUAL => "USUAL_USDT",
            GateBaseAssetCurrency::MERL => "MERL_USDT",
            GateBaseAssetCurrency::STRK => "STRK_USDT",
            GateBaseAssetCurrency::STX => "STX_USDT",
            GateBaseAssetCurrency::ZKJ => "ZKJ_USDT",
            GateBaseAssetCurrency::ALT => "ALT_USDT",
            GateBaseAssetCurrency::THETA => "THETA_USDT",
            GateBaseAssetCurrency::GMT => "GMT_USDT",
            GateBaseAssetCurrency::CATI => "CATI_USDT",
            GateBaseAssetCurrency::IOTA => "IOTA_USDT",
            GateBaseAssetCurrency::ASR => "ASR_USDT",
            GateBaseAssetCurrency::CTSI => "CTSI_USDT",
            GateBaseAssetCurrency::FIS => "FIS_USDT",
            GateBaseAssetCurrency::USDC => "USDC_USDT",
            GateBaseAssetCurrency::OGN => "OGN_USDT",
            GateBaseAssetCurrency::IO => "IO_USDT",
            GateBaseAssetCurrency::SOMI => "SOMI_USDT",
            GateBaseAssetCurrency::MORPHO => "MORPHO_USDT",
            GateBaseAssetCurrency::EPIC => "EPIC_USDT",
            GateBaseAssetCurrency::ID => "ID_USDT",
            GateBaseAssetCurrency::KAVA => "KAVA_USDT",
            GateBaseAssetCurrency::HFT => "HFT_USDT",
            GateBaseAssetCurrency::SAGA => "SAGA_USDT",
            GateBaseAssetCurrency::JASMY => "JASMY_USDT",
            GateBaseAssetCurrency::LAYER => "LAYER_USDT",
            GateBaseAssetCurrency::HEI => "HEI_USDT",
        }
    }
    pub fn from_usize(num: usize) -> Option<Self> {
        match num {
            0 => Some(GateBaseAssetCurrency::SOL),
            1 => Some(GateBaseAssetCurrency::LINK),
            2 => Some(GateBaseAssetCurrency::SUI),
            3 => Some(GateBaseAssetCurrency::AVAX),
            4 => Some(GateBaseAssetCurrency::IP),
            5 => Some(GateBaseAssetCurrency::FARTCOIN),
            6 => Some(GateBaseAssetCurrency::LRC),
            7 => Some(GateBaseAssetCurrency::BIO),
            8 => Some(GateBaseAssetCurrency::ARB),
            9 => Some(GateBaseAssetCurrency::WIF),
            10 => Some(GateBaseAssetCurrency::DOT),
            11 => Some(GateBaseAssetCurrency::UNI),
            12 => Some(GateBaseAssetCurrency::FORM),
            13 => Some(GateBaseAssetCurrency::OP),
            14 => Some(GateBaseAssetCurrency::WLD),
            15 => Some(GateBaseAssetCurrency::QTUM),
            16 => Some(GateBaseAssetCurrency::NEAR),
            17 => Some(GateBaseAssetCurrency::LDO),
            18 => Some(GateBaseAssetCurrency::ONDO),
            19 => Some(GateBaseAssetCurrency::TIA),
            20 => Some(GateBaseAssetCurrency::ZRX),
            21 => Some(GateBaseAssetCurrency::RLC),
            22 => Some(GateBaseAssetCurrency::BERA),
            23 => Some(GateBaseAssetCurrency::PENDLE),
            24 => Some(GateBaseAssetCurrency::W),
            25 => Some(GateBaseAssetCurrency::API3),
            26 => Some(GateBaseAssetCurrency::FET),
            27 => Some(GateBaseAssetCurrency::JUP),
            28 => Some(GateBaseAssetCurrency::ATOM),
            29 => Some(GateBaseAssetCurrency::MYX),
            30 => Some(GateBaseAssetCurrency::NEO),
            31 => Some(GateBaseAssetCurrency::OM),
            32 => Some(GateBaseAssetCurrency::JOE),
            33 => Some(GateBaseAssetCurrency::SAND),
            34 => Some(GateBaseAssetCurrency::EIGEN),
            35 => Some(GateBaseAssetCurrency::RED),
            36 => Some(GateBaseAssetCurrency::AI),
            37 => Some(GateBaseAssetCurrency::MAGIC),
            38 => Some(GateBaseAssetCurrency::AERO),
            39 => Some(GateBaseAssetCurrency::MELANIA),
            40 => Some(GateBaseAssetCurrency::RENDER),
            41 => Some(GateBaseAssetCurrency::JTO),
            42 => Some(GateBaseAssetCurrency::ONT),
            43 => Some(GateBaseAssetCurrency::CELO),
            44 => Some(GateBaseAssetCurrency::APE),
            45 => Some(GateBaseAssetCurrency::S),
            46 => Some(GateBaseAssetCurrency::ALPINE),
            47 => Some(GateBaseAssetCurrency::KAITO),
            48 => Some(GateBaseAssetCurrency::SUSHI),
            49 => Some(GateBaseAssetCurrency::ARKM),
            50 => Some(GateBaseAssetCurrency::UMA),
            51 => Some(GateBaseAssetCurrency::ZRO),
            52 => Some(GateBaseAssetCurrency::HOOK),
            53 => Some(GateBaseAssetCurrency::XTZ),
            54 => Some(GateBaseAssetCurrency::USUAL),
            55 => Some(GateBaseAssetCurrency::MERL),
            56 => Some(GateBaseAssetCurrency::STRK),
            57 => Some(GateBaseAssetCurrency::STX),
            58 => Some(GateBaseAssetCurrency::ZKJ),
            59 => Some(GateBaseAssetCurrency::ALT),
            60 => Some(GateBaseAssetCurrency::THETA),
            61 => Some(GateBaseAssetCurrency::GMT),
            62 => Some(GateBaseAssetCurrency::CATI),
            63 => Some(GateBaseAssetCurrency::IOTA),
            64 => Some(GateBaseAssetCurrency::ASR),
            65 => Some(GateBaseAssetCurrency::CTSI),
            66 => Some(GateBaseAssetCurrency::FIS),
            67 => Some(GateBaseAssetCurrency::USDC),
            68 => Some(GateBaseAssetCurrency::OGN),
            69 => Some(GateBaseAssetCurrency::IO),
            70 => Some(GateBaseAssetCurrency::SOMI),
            71 => Some(GateBaseAssetCurrency::MORPHO),
            72 => Some(GateBaseAssetCurrency::EPIC),
            73 => Some(GateBaseAssetCurrency::ID),
            74 => Some(GateBaseAssetCurrency::KAVA),
            75 => Some(GateBaseAssetCurrency::HFT),
            76 => Some(GateBaseAssetCurrency::SAGA),
            77 => Some(GateBaseAssetCurrency::JASMY),
            78 => Some(GateBaseAssetCurrency::LAYER),
            79 => Some(GateBaseAssetCurrency::HEI),
            _ => None,
        }
    }
}
// bn and gate bbo map
 //[
//(bn_best_bid_price, bn_best_bid_qty, bn_best_ask_price, bn_best_ask_qty), 
//(gate_best_bid_price, gate_best_bid_qty, gate_best_ask_price, gate_best_ask_qty)]
pub static mut BN_AND_GATE_BBO_MAP: [([f64; 4], [f64; 4]); 80] = [([0.0, 0.0, 0.0, 0.0], [0.0, 0.0, 0.0, 0.0]); 80];
// req id, order id, order price, order status(0 for pending, 1 for ack
pub static mut GATE_PENDING_ORDERS: [(u64, u64, f64, u8); 80] = [(0, 0, 0.0, 0); 80];
pub static GATE_PRICE_TICKS: [f64; 80] = [
    0.01f64, // SOL
    0.001f64, // LINK
    0.0001f64, // SUI
    0.01f64, // AVAX
    0.001f64, // IP
    0.0001f64, // FARTCOIN
    0.00001f64, // LRC
    0.00001f64, // BIO
    0.0001f64, // ARB
    0.0001f64, // WIF
    0.001f64, // DOT
    0.001f64, // UNI
    0.0001f64, // FORM
    0.0001f64, // OP
    0.0001f64, // WLD
    0.001f64, // QTUM
    0.001f64, // NEAR
    0.0001f64, // LDO
    0.0001f64, // ONDO
    0.001f64, // TIA
    0.0001f64, // ZRX
    0.0001f64, // RLC
    0.001f64, // BERA
    0.001f64, // PENDLE
    0.00001f64, // W
    0.0001f64, // API3
    0.0001f64, // FET
    0.0001f64, // JUP
    0.001f64, // ATOM
    0.0001f64, // MYX
    0.001f64, // NEO
    0.0001f64, // OM
    0.0001f64, // JOE
    0.0001f64, // SAND
    0.0001f64, // EIGEN
    0.0001f64, // RED
    0.00001f64, // AI
    0.00001f64, // MAGIC
    0.0001f64, // AERO
    0.0001f64, // MELANIA
    0.001f64, // RENDER
    0.001f64, // JTO
    0.00001f64, // ONT
    0.0001f64, // CELO
    0.0001f64, // APE
    0.0001f64, // S
    0.0001f64, // ALPINE
    0.0001f64, // KAITO
    0.0001f64, // SUSHI
    0.0001f64, // ARKM
    0.0001f64, // UMA
    0.001f64, // ZRO
    0.00001f64, // HOOK
    0.0001f64, // XTZ
    0.00001f64, // USUAL
    0.00001f64, // MERL
    0.00001f64, // STRK
    0.0001f64, // STX
    0.0001f64, // ZKJ
    0.00001f64, // ALT
    0.0001f64, // THETA
    0.00001f64, // GMT
    0.00001f64, // CATI
    0.0001f64, // IOTA
    0.001f64, // ASR
    0.00001f64, // CTSI
    0.00001f64, // FIS
    0.0001f64, // USDC
    0.00001f64, // OGN
    0.0001f64, // IO
    0.0001f64, // SOMI
    0.0001f64, // MORPHO
    0.0001f64, // EPIC
    0.0001f64, // ID
    0.0001f64, // KAVA
    0.00001f64, // HFT
    0.0001f64, // SAGA
    0.000001f64, // JASMY
    0.0001f64, // LAYER
    0.0001f64, // HEI
];
