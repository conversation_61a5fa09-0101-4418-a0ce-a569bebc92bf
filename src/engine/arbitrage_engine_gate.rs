use crate::{
    encoding::{
        book_ticker::FuturesBookTicker,
        gate::{self, GateBookTicker},
    },
    engine::gate_const::{
        BN_AND_GATE_BBO_MAP, CURRENCY_LEN, GATE_PENDING_ORDERS, GATE_PRICE_TICKS,
        GateBaseAssetCurrency,
    },
    utils::perf::now,
};

fn find_nearest_price_by_tick(price: f64, tick: f64) -> f64 {
    let mut nearest_price = price;
    loop {
        nearest_price -= tick;
        if nearest_price < price * 0.9992 {
            return nearest_price;
        }
    }
}

pub struct GateMakerOrder {
    pub req_id: u64,
    pub order_id: u64,
    pub price: f64,
    pub status: gate::UnifiedOrderStatus,
}

pub struct BnHedgeOrder {
    pub qty: f64,
}

pub struct ArbitrageGate {}

impl ArbitrageGate {
    pub fn new() -> Self {
        Self {}
    }

    pub fn update_bn_bbo(&mut self, bbo: &FuturesBookTicker) -> Option<GateMakerOrder> {
        let currency = match GateBaseAssetCurrency::from_symbol(&bbo.symbol) {
            Some(currency) => currency,
            None => return None,
        };
        let index = currency as usize;
        unsafe {
            BN_AND_GATE_BBO_MAP[index].0 = [bbo.bid_price, bbo.bid_qty, bbo.ask_price, bbo.ask_qty];
            let order = GATE_PENDING_ORDERS[index];

            let tick = GATE_PRICE_TICKS[index];
            let req_id = now();
            if order.0 == 0 {
                let order_price = find_nearest_price_by_tick(bbo.bid_price, tick);
                crate::info_unsafe!(
                    "placing gate order: bn price: {} gate price: {}",
                    bbo.bid_price,
                    order_price,
                );
                GATE_PENDING_ORDERS[index].0 = req_id;
                GATE_PENDING_ORDERS[index].3 = 0;
                let order = GateMakerOrder {
                    req_id,
                    order_id: 0,
                    price: order_price,
                    status: gate::UnifiedOrderStatus::New,
                };
                return Some(order);
            }

            if order.3 == 0 {
                // pending orders
                return None;
            }

            let diff_ratio = (bbo.bid_price - order.2) / bbo.bid_price;
            if diff_ratio > 0.001 || diff_ratio < 0.0008 {
                GATE_PENDING_ORDERS[index].3 = 0;
                GATE_PENDING_ORDERS[index].0 = req_id;
                let order_price = find_nearest_price_by_tick(bbo.bid_price, tick);
                crate::info_unsafe!(
                    "placing gate order: bn price: {} gate price: {}",
                    bbo.bid_price,
                    order_price,
                );
                let order = GateMakerOrder {
                    req_id,
                    order_id: GATE_PENDING_ORDERS[index].1,
                    price: order_price,
                    status: gate::UnifiedOrderStatus::Open,
                };
                return Some(order);
            }
        }
        None
    }

    pub fn update_gate_bbo(&mut self, bbo: GateBookTicker) {
        let currency = match GateBaseAssetCurrency::from_symbol(&bbo.symbol) {
            Some(currency) => currency,
            None => return,
        };
        let index = currency as usize;
        crate::info!("update gate bbo: {}", bbo.symbol);
        unsafe {
            BN_AND_GATE_BBO_MAP[index].1 = [bbo.bid_price, bbo.bid_qty, bbo.ask_price, bbo.ask_qty];
        }
        let order = unsafe { GATE_PENDING_ORDERS[index] };
        if order.3 == 0 {
            // pending order
            return;
        }
        if bbo.bid_price <= order.2 {
            unsafe {
                GATE_PENDING_ORDERS[index].3 = 0;
                crate::info_unsafe!("order filled {}", order.1);
            }
        }
    }

    pub fn update_order_status(
        &mut self,
        status: gate::UnifiedOrderStatus,
        symbol: &str,
        order_id: u64,
        req_id: u64,
        price: f64,
    ) {
        let currency = match GateBaseAssetCurrency::from_symbol(symbol) {
            Some(currency) => currency,
            None => {
                let mut target = 10000;
                let len = CURRENCY_LEN;
                for i in 0..len {
                    let p = unsafe { GATE_PENDING_ORDERS[i] };
                    if p.0 == req_id {
                        target = i;
                        break;
                    }
                }
                if target != 10000 {
                    GateBaseAssetCurrency::from_usize(target).unwrap()
                } else {
                    crate::error!("unknown symbol {}", req_id);
                    return;
                }
            }
        };
        let index = currency as usize;
        match status {
            gate::UnifiedOrderStatus::Open => unsafe {
                if GATE_PENDING_ORDERS[index].3 != 0 {
                    crate::error_unsafe!("order opened but not pending, {} {}", order_id, symbol);
                    return;
                }
                GATE_PENDING_ORDERS[index].2 = price;
                GATE_PENDING_ORDERS[index].3 = 1;
            },
            gate::UnifiedOrderStatus::Filled => unsafe {
                crate::info_unsafe!("order filled {} {} {}", order_id, symbol, price);
                GATE_PENDING_ORDERS[index].3 = 0;
            },
            gate::UnifiedOrderStatus::Canceled | gate::UnifiedOrderStatus::Expired => unsafe {
                crate::info_unsafe!("order canceled {} {} {}", order_id, symbol, price);
                GATE_PENDING_ORDERS[index].3 = 0;
            },
            _ => {}
        }
    }
}
