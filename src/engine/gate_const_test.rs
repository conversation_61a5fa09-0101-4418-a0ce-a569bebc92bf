#![cfg(test)]

use crate::engine::gate_const::GateBaseAssetCurrency;

#[test]
fn test_gate_base_asset_currency() {
    assert_eq!(
        GateBaseAssetCurrency::from_symbol("KAITOUSDT"),
        Some(GateBaseAssetCurrency::KAITO)
    );
    assert_eq!(
        GateBaseAssetCurrency::from_symbol("KAITO_USDT"),
        Some(GateBaseAssetCurrency::KAITO)
    );
    assert_eq!(
        GateBaseAssetCurrency::from_symbol("KAITO"),
        Some(GateBaseAssetCurrency::KAITO)
    );
    assert_eq!(GateBaseAssetCurrency::from_symbol("KAITO_USDT_"), None);
}
