use ring::hmac;
use serde::Deserialize;
use serde_json::json;

use crate::{
    encoding::{
        futures_order::OrderSide,
        price::{RoundingMode, format_price_with_tick_mode},
    },
    engine::gate_const::{GATE_BASE_ASSETS, GATE_PRICE_TICKS, GateBaseAssetCurrency},
    utils::perf::{system_now_in_ms, system_now_in_secs},
};

const GATE_USER_ID: &str = "36554434";
const GATE_API_KEY: &str = "efb5fb382886a7bb26a3302f22a7a4a7";
const GATE_SECRET_KEY: &str = "adc72b8896c9692114f35cf330a9ee8775d2a200b1b540eba5e33f112230eca6";
// https://api.gateio.ws/api/v4/futures/usdt/contracts/LTC_USDT

#[allow(dead_code)]
#[derive(Debug, Deserialize)]
struct Envelope {
    #[allow(dead_code)]
    time: Option<i64>,
    #[allow(dead_code)]
    time_ms: Option<i64>,
    channel: Option<String>,
    event: Option<String>,
    result: Option<serde_json::Value>,
    // 也可能返回 "error"
    error: Option<serde_json::Value>,
}

#[derive(Debug, Deserialize)]
pub struct GateBookTicker {
    /// 生成时间(毫秒)
    #[serde(default, rename = "t")]
    pub ts: u64,

    /// order book update id
    #[serde(default, rename = "u")]
    pub update_id: serde_json::Value,

    /// 合约名，如 "BTC_USDT"
    #[serde(default, rename = "s")]
    pub symbol: String,

    /// 最优买价
    #[serde(
        default,
        rename = "b",
        deserialize_with = "serde_aux::field_attributes::deserialize_number_from_string"
    )]
    pub bid_price: f64,

    /// 最优买量
    #[serde(
        default,
        rename = "B",
        deserialize_with = "serde_aux::field_attributes::deserialize_number_from_string"
    )]
    pub bid_qty: f64,

    /// 最优卖价
    #[serde(
        default,
        rename = "a",
        deserialize_with = "serde_aux::field_attributes::deserialize_number_from_string"
    )]
    pub ask_price: f64,

    /// 最优卖量
    #[serde(
        default,
        rename = "A",
        deserialize_with = "serde_aux::field_attributes::deserialize_number_from_string"
    )]
    pub ask_qty: f64,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum UnifiedOrderStatus {
    New,
    Open,
    Filled,
    Canceled,
    Expired,
}

#[allow(dead_code)]
#[derive(Debug, Deserialize)]
pub struct FuturesOrder {
    contract: String,
    id: u64,
    size: i64,
    price: f64,
    pub status: String,
    pub tif: String,
    #[serde(default)]
    pub text: String,

    #[serde(default)]
    create_time_ms: Option<i64>,
    #[serde(default)]
    pub finish_as: Option<String>,
    #[serde(default)]
    finish_time_ms: Option<i64>,
    #[serde(default)]
    fill_price: Option<f64>,
    #[serde(default)]
    pub left: Option<i64>,
    #[serde(default)]
    tkfr: Option<f64>,
    #[serde(default)]
    mkfr: Option<f64>,
    #[serde(default)]
    user: Option<String>,
    #[serde(default)]
    update_id: Option<u64>,
    #[serde(default)]
    update_time: Option<i64>,
    #[serde(default)]
    stop_loss_price: Option<String>,
    #[serde(default)]
    stop_profit_price: Option<String>,
    #[serde(default)]
    is_close: Option<bool>,
    #[serde(default)]
    is_reduce_only: Option<bool>,
    #[serde(default)]
    is_liq: Option<bool>,
    #[serde(default)]
    refu: Option<u64>,
    #[serde(default)]
    refr: Option<f64>,
    #[serde(default)]
    iceberg: Option<i64>,
    #[serde(default)]
    biz_info: Option<serde_json::Value>,
}

#[allow(dead_code)]
#[derive(Debug, Deserialize)]
struct WsMsg<T> {
    channel: String,
    event: String,
    #[serde(default)]
    time: Option<i64>,
    #[serde(default)]
    time_ms: Option<i64>,
    result: T,
}

pub fn decode_bbo(input: &[u8]) -> Option<GateBookTicker> {
    match serde_json::from_slice::<Envelope>(input) {
        Ok(env) => {
            if env.channel.as_deref() == Some("futures.book_ticker")
                && env.event.as_deref() == Some("update")
            {
                if let Some(res) = env.result {
                    if let Ok(bt) = serde_json::from_value::<GateBookTicker>(res) {
                        return Some(bt);
                    }
                }
            }
            None
        }
        Err(_) => None,
    }
}

fn gate_sign(secret: &str, event: &str, channel: &str, req_param: &[u8], ts: &str) -> String {
    let key = hmac::Key::new(hmac::HMAC_SHA512, secret.as_bytes());

    // 构造消息
    let mut msg = Vec::with_capacity(event.len() + channel.len() + req_param.len() + ts.len() + 3);
    msg.extend_from_slice(event.as_bytes());
    msg.extend_from_slice(b"\n");
    msg.extend_from_slice(channel.as_bytes());
    msg.extend_from_slice(b"\n");
    msg.extend_from_slice(req_param);
    msg.extend_from_slice(b"\n");
    msg.extend_from_slice(ts.as_bytes());

    let tag = hmac::sign(&key, &msg);
    hex::encode(tag.as_ref())
}

/*
{
  "time": 1735516800,
  "channel": "futures.login",
  "event": "api",
  "payload": {
    "api_key": "YOUR_API_KEY",
    "signature": "YOUR_HEX_SIGNATURE",
    "timestamp": "1735516800",
    "req_id": "login-001"
  }
} */
pub fn generate_login_request() -> String {
    let ts_sec = system_now_in_ms() / 1000;
    let ts_sec_str = ts_sec.to_string();
    let channel = "futures.login";
    let event = "api";
    let signature = gate_sign(GATE_SECRET_KEY, event, channel, &vec![], &ts_sec_str);
    format!(
        r#"
{{
    "time": {},
    "channel": "{}",
    "event": "{}",
    "payload": {{
        "api_key": "{}",
        "signature": "{}",
        "timestamp": "{}",
        "req_id": "{}"
    }}
}}"#,
        ts_sec, channel, event, GATE_API_KEY, signature, ts_sec_str, ts_sec_str
    )
}

pub fn place_order(req_id: u64, price: f64, side: OrderSide, tif: &str, symbol: &str) -> String {
    let id = system_now_in_secs();
    let tick = GATE_PRICE_TICKS[GateBaseAssetCurrency::from_symbol(symbol).unwrap() as usize];
    let price = format_price_with_tick_mode(price, tick, RoundingMode::Floor);
    let size = match side {
        OrderSide::Buy => 1,
        OrderSide::Sell => -1,
    };
    format!(
        r#"
{{
    "time": {},
    "channel": "futures.order_place",
    "event": "api",
    "payload": {{
        "req_id": "{}",
        "req_param": {{
            "contract": "{}",
            "size": {},
            "price": "{}",
            "tif": "{}"
        }}
    }}
}}"#,
        id, req_id, symbol, size, price, tif,
    )
}

pub fn modify_order(req_id: u64, price: f64, order_id: u64, symbol: &str) -> String {
    let time_in_secs = system_now_in_secs();
    let tick = GATE_PRICE_TICKS[GateBaseAssetCurrency::from_symbol(symbol).unwrap() as usize];
    let price = format_price_with_tick_mode(price, tick, RoundingMode::Floor);
    format!(
        r#"
{{
    "time": {},
    "channel": "futures.order_amend",
    "event": "api",
    "payload": {{
        "req_id": "{}",
        "req_param": {{
            "order_id": {},
            "price": "{}"
        }}
    }}
}}"#,
        time_in_secs, req_id, order_id, price,
    )
}

fn gen_sign(secret: &str, channel: &str, event: &str, ts_sec: u64) -> String {
    let key = hmac::Key::new(hmac::HMAC_SHA512, secret.as_bytes());
    let message = format!("channel={}&event={}&time={}", channel, event, ts_sec);
    let tag = hmac::sign(&key, &message.as_bytes());
    hex::encode(tag.as_ref())
}

pub fn generate_order_sub_request() -> String {
    let ts_sec = system_now_in_secs();
    let channel = "futures.orders";
    let event = "subscribe";
    let sign = gen_sign(GATE_SECRET_KEY, channel, event, ts_sec);
    format!(
        r#"
{{
    "time": {},
    "channel": "{}",
    "event": "{}",
    "payload": ["{}", "all"],
    "auth": {{
        "method": "api_key",
        "KEY": "{}",
        "SIGN": "{}"
    }}
}}"#,
        ts_sec, channel, event, GATE_USER_ID, GATE_API_KEY, sign
    )
}

pub fn generate_bbo_subscribe_request() -> String {
    let payload = GATE_BASE_ASSETS
        .iter()
        .map(|s| format!("{}_USDT", s))
        .collect::<Vec<_>>();
    let subscribe_msg = json!({
        "time": chrono::Utc::now().timestamp(),
        "channel": "futures.book_ticker",
        "event": "subscribe",
        "payload": payload,
    });
    subscribe_msg.to_string()
}

pub fn parse_order_update(input: &[u8]) -> Option<Vec<FuturesOrder>> {
    let v: serde_json::Value = match serde_json::from_slice(input) {
        Ok(v) => v,
        Err(_) => return None,
    };
    let channel = v
        .get("channel")
        .and_then(|x| x.as_str())
        .unwrap_or_default();
    let event = v.get("event").and_then(|x| x.as_str()).unwrap_or_default();

    if channel == "futures.orders" && event == "subscribe" {
        return None;
    }
    if channel == "futures.orders" && event == "update" {
        let msg: WsMsg<Vec<FuturesOrder>> = match serde_json::from_value(v) {
            Ok(msg) => msg,
            Err(_) => return None,
        };
        return Some(msg.result);
    }
    None
}

pub fn unify_gate_status(
    status: &str,
    finish_as: &Option<String>,
    left: Option<i64>,
    tif: &String,
) -> UnifiedOrderStatus {
    let s = status.to_ascii_lowercase();
    if s == "open" {
        return UnifiedOrderStatus::Open;
    }
    if s == "finished" {
        // 先看是否实满成交
        if finish_as
            .as_deref()
            .map(|x| x.eq_ignore_ascii_case("filled"))
            .unwrap_or(false)
            || left == Some(0)
        {
            return UnifiedOrderStatus::Filled;
        }

        let fa = finish_as
            .as_ref()
            .map(|x| x.to_ascii_lowercase())
            .unwrap_or_default();

        // 系统/人工取消类
        let is_canceled = matches!(
            fa.as_str(),
            "cancelled" | "canceled" | "liquidated" | "reduce_only" | "adl" | "auto_deleveraged"
        );

        if is_canceled {
            return UnifiedOrderStatus::Canceled;
        }

        // 规则或时效导致未成交（等价“过期”）
        let tif_l = tif.to_ascii_lowercase();
        let is_expired = matches!(
            fa.as_str(),
            "ioc" | "post_only" | "poc" | "po" | "gtx" | "expired"
        ) || matches!(tif_l.as_str(), "ioc" | "gtx");

        if is_expired {
            return UnifiedOrderStatus::Expired;
        }

        return UnifiedOrderStatus::Canceled;
    }

    UnifiedOrderStatus::Open
}

pub fn generate_ping_req() -> String {
    json!({"time": system_now_in_secs(), "channel": "futures.ping"}).to_string()
}

fn de_f64_from_any<'de, D>(de: D) -> Result<f64, D::Error>
where
    D: serde::Deserializer<'de>,
{
    use serde::Deserialize;
    match serde_json::Value::deserialize(de)? {
        serde_json::Value::Number(n) => n
            .as_f64()
            .ok_or_else(|| serde::de::Error::custom("invalid f64")),
        serde_json::Value::String(s) => s
            .parse::<f64>()
            .map_err(|_| serde::de::Error::custom("invalid f64 string")),
        v => Err(serde::de::Error::custom(format!(
            "expected number/string, got {v}"
        ))),
    }
}

fn de_u64_from_any<'de, D>(de: D) -> Result<u64, D::Error>
where
    D: serde::Deserializer<'de>,
{
    use serde::Deserialize;
    match serde_json::Value::deserialize(de)? {
        serde_json::Value::Number(n) => n
            .as_u64()
            .ok_or_else(|| serde::de::Error::custom("invalid u64")),
        serde_json::Value::String(s) => s
            .parse::<u64>()
            .map_err(|_| serde::de::Error::custom("invalid u64 string")),
        v => Err(serde::de::Error::custom(format!(
            "expected number/string, got {v}"
        ))),
    }
}

// =============== 你发出去的 req_param 结构（ack 时会原样回显） ===============
#[derive(Debug, Deserialize, Clone)]
pub struct OrderPlaceParam {
    pub contract: String,
    pub size: i64,
    #[serde(deserialize_with = "de_f64_from_any")]
    pub price: f64,
    pub tif: String,
    #[serde(default)]
    pub text: Option<String>,
    // 这里保留空间：iceberg、stp_act、reduce_only 等需要时再加
}

// =============== 最终响应里的订单对象（常用字段；其余用 Option 兜底） ===============
#[derive(Debug, Deserialize, Clone)]
pub struct OrderEntity {
    #[serde(deserialize_with = "de_u64_from_any")]
    pub id: u64,
    pub contract: String,
    pub size: i64,
    #[serde(deserialize_with = "de_f64_from_any")]
    pub price: f64,
    pub tif: String,

    pub status: String, // "open" / "finished"
    #[serde(default)]
    pub finish_as: Option<String>, // "filled" / "canceled" / "ioc" / "gtx" / "expired" ...
    #[serde(default)]
    pub left: Option<i64>, // 剩余张数
    #[serde(default)]
    pub user: Option<String>, // 文档例子里有时是数字/字符串
    #[serde(default)]
    pub create_time_ms: Option<i64>,
    #[serde(default)]
    pub finish_time_ms: Option<i64>,
    #[serde(default)]
    pub update_id: Option<u64>,
    #[serde(default)]
    pub text: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct WsHeader {
    pub response_time: String,
    pub status: String,
    pub channel: String,
    pub event: String,
    #[serde(default)]
    pub client_id: Option<String>,
    #[serde(default)]
    pub conn_id: Option<String>,
    #[serde(default)]
    pub conn_trace_id: Option<String>,
    #[serde(default)]
    pub trace_id: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct WsErr {
    pub label: String,
    pub message: String,
}

#[allow(dead_code)]
#[derive(Debug, Deserialize)]
struct WsData<R> {
    pub result: Option<R>,
    #[serde(default)]
    pub errs: Option<WsErr>,
}

#[allow(dead_code)]
#[derive(Debug, Deserialize)]
#[serde(bound(deserialize = "R: serde::de::DeserializeOwned"))] // 或 "R: Deserialize<'de>"
struct OrderPlaceEnvelope<R> {
    pub request_id: String,
    #[serde(default)]
    pub ack: Option<bool>, // 文档：ack 仅在确认消息里返回；最终响应通常不返回该字段。:contentReference[oaicite:1]{index=1}
    pub header: WsHeader,
    pub data: WsData<R>,
}

// =============== 统一后的高层结果枚举（你直接用这个） ===============
#[derive(Debug)]
pub enum OrderPlaceMsg {
    /// ack 确认：data.result 回显的是你发出去的 req_param
    Ack {
        request_id: String,
        header: WsHeader,
        req_param: OrderPlaceParam,
    },
    /// 最终响应：data.result 是订单对象（可能已成交/部分成交/挂单）
    Response {
        request_id: String,
        header: WsHeader,
        order: OrderEntity,
    },
    /// 出错：data.errs 携带 label/message
    Error {
        request_id: String,
        header: WsHeader,
        err: WsErr,
    },
}

/// 解析一条 futures.order_place 的回包（ack 或最终响应）
/// 用法： let msg = parse_order_place(json_str)?;
pub fn parse_order_place(json: &[u8]) -> Option<OrderPlaceMsg> {
    let v: serde_json::Value = match serde_json::from_slice(json) {
        Ok(v) => v,
        Err(_) => return None,
    };

    // 公共元信息
    let request_id = v
        .get("request_id")
        .and_then(|x| x.as_str())
        .unwrap_or_default()
        .to_string();
    let header: WsHeader = match serde_json::from_value(match v.get("header").cloned() {
        Some(v) => v,
        None => return None,
    }) {
        Ok(v) => v,
        Err(_) => return None,
    };

    // data.errs -> Error
    if let Some(errs) = v.get("data").and_then(|d| d.get("errs")).cloned() {
        let err: WsErr = match serde_json::from_value(errs) {
            Ok(v) => v,
            Err(_) => return None,
        };
        return Some(OrderPlaceMsg::Error {
            request_id,
            header,
            err,
        });
    }

    // 存在 ack 字段（无论 true/false）都按 ack 处理；文档处写 ack 出现在确认消息里。:contentReference[oaicite:2]{index=2}
    let is_ack = v.get("ack").is_some();

    if is_ack {
        // ack: result 回显的是 req_param
        let env: OrderPlaceEnvelope<OrderPlaceParam> = match serde_json::from_value(v) {
            Ok(v) => v,
            Err(_) => return None,
        };
        let req_param = match env.data.result {
            Some(v) => v,
            None => return None,
        };
        return Some(OrderPlaceMsg::Ack {
            request_id: env.request_id,
            header: env.header,
            req_param,
        });
    } else {
        // 最终响应：result 是订单对象
        let env: OrderPlaceEnvelope<OrderEntity> = match serde_json::from_value(v) {
            Ok(v) => v,
            Err(_) => return None,
        };
        let order = match env.data.result {
            Some(v) => v,
            None => return None,
        };
        return Some(OrderPlaceMsg::Response {
            request_id: env.request_id,
            header: env.header,
            order,
        });
    }
}
