use libwebsocket_rs::{
    CallbackData, Message, Result, Settings, WebSocket, WebSocketHandle,
    encoding::{
        book_ticker::parse_futures_bookticker,
        futures_order::OrderSide,
        gate::{self, OrderPlaceMsg, UnifiedOrderStatus},
    },
    engine::{
        arbitrage_engine_gate::ArbitrageGate,
        binance::generate_futures_book_ticker_url,
        gate_const::GATE_BASE_ASSETS,
        token::{ORDER_TOKEN_1, ORDER_TOKEN_2, WS_BBO_1, WS_BBO_2},
    },
    error, flush_logs, info,
    net::utils::url::Url,
    utils::perf::system_now_in_secs,
};

const GATE_WS_URL: &str = "wss://fx-ws.gateio.ws/v4/ws/usdt";
const IN_LEN: usize = 1024 * 32;
const OUT_LEN: usize = 1024 * 4;

fn generate_bbo_url() -> String {
    GATE_WS_URL.to_string()
}

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    let mut engine = ArbitrageGate::new();
    let mut last_ping_time_in_secs = system_now_in_secs();
    let callback =
        move |handle: &mut WebSocketHandle<IN_LEN, OUT_LEN>, cd: CallbackData| -> Result<()> {
            match cd {
                CallbackData::Message(token, msg) => match msg {
                    Message::WebsocketPayload(data) => match token {
                        WS_BBO_1 => match gate::decode_bbo(data.as_ref()) {
                            Some(bt) => {
                                engine.update_gate_bbo(bt);
                            }
                            None => {
                                libwebsocket_rs::debug!(
                                    "failed to parse gate bbo: {}",
                                    String::from_utf8_lossy(data.as_ref())
                                );
                            }
                        },
                        WS_BBO_2 => {
                            if let Some(bt) = parse_futures_bookticker(data.as_ref()) {
                                if let Some((req_id, order_id, order_price, new_order)) =
                                    engine.update_bn_bbo(&bt)
                                {
                                    if new_order {
                                        let order_req = gate::place_order(
                                            req_id,
                                            order_price,
                                            OrderSide::Buy,
                                            "poc",
                                            &bt.symbol,
                                        );
                                        info!("order place request: {}", order_req);
                                        handle.send_message(ORDER_TOKEN_1, order_req)?;
                                    } else {
                                        let order_req = gate::modify_order(
                                            req_id,
                                            order_price,
                                            order_id,
                                            &bt.symbol,
                                        );
                                        info!("order amend request: {}", order_req);
                                        handle.send_message(ORDER_TOKEN_1, order_req)?;
                                    }
                                }
                            } else {
                                info!(
                                    "failed to parse bbo: {}",
                                    String::from_utf8_lossy(data.as_ref())
                                );
                            }
                            if system_now_in_secs() - last_ping_time_in_secs > 10 {
                                handle.send_message(ORDER_TOKEN_1, gate::generate_ping_req())?;
                                handle.send_message(ORDER_TOKEN_2, gate::generate_ping_req())?;
                                handle.send_message(WS_BBO_1, gate::generate_ping_req())?;
                                last_ping_time_in_secs = system_now_in_secs();
                            }
                        }
                        ORDER_TOKEN_1 => match gate::parse_order_place(data.as_ref()) {
                            Some(msg) => match msg {
                                OrderPlaceMsg::Ack { .. } => {
                                    crate::info!("order ack: {:?}", msg);
                                }
                                OrderPlaceMsg::Response {
                                    request_id: req_id,
                                    header: _,
                                    order: rsp,
                                } => {
                                    let status = gate::unify_gate_status(
                                        rsp.status.as_ref(),
                                        &rsp.finish_as,
                                        rsp.left,
                                        &rsp.tif,
                                    );
                                    let order_id = rsp.id;
                                    let req_id = match req_id.parse::<u64>() {
                                        Ok(id) => id,
                                        Err(_) => {
                                            crate::error!("parse req id error: {}", req_id);
                                            return Ok(());
                                        }
                                    };
                                    info!("order rsp id: {} status: {:?}", rsp.id, status);
                                    engine.update_order_status(
                                        status,
                                        &rsp.contract,
                                        order_id,
                                        req_id,
                                        rsp.price,
                                    );
                                }
                                OrderPlaceMsg::Error {
                                    request_id: req_id,
                                    header: _,
                                    err,
                                } => {
                                    let req_id = match req_id.parse::<u64>() {
                                        Ok(id) => id,
                                        Err(_) => {
                                            crate::error!("parse req id error: {}", req_id);
                                            return Ok(());
                                        }
                                    };
                                    info!("order place error: {:?}", err);
                                    engine.update_order_status(
                                        UnifiedOrderStatus::Canceled,
                                        "",
                                        0,
                                        req_id,
                                        0.0,
                                    );
                                }
                            },
                            None => {
                                libwebsocket_rs::debug!(
                                    "failed to parse order place response: {}",
                                    String::from_utf8_lossy(data.as_ref())
                                );
                            }
                        },
                        ORDER_TOKEN_2 => {
                            if let Some(orders) = gate::parse_order_update(data.as_ref()) {
                                for order in orders {
                                    let status = gate::unify_gate_status(
                                        order.status.as_ref(),
                                        &order.finish_as,
                                        order.left,
                                        &order.tif,
                                    );
                                    info!("order update: {:?}, {:?}", order.text, status);
                                    // if let Some(result) = engine.update_order_status(status) {
                                    //     handle.send_message(ORDER_TOKEN_1, order_req)?;
                                    // }
                                }
                            } else {
                                libwebsocket_rs::debug!(
                                    "order sub response: {}",
                                    String::from_utf8_lossy(data.as_ref())
                                );
                                crate::flush_logs!();
                            }
                        }
                        _ => (),
                    },
                    _ => (),
                },
                CallbackData::ConnectionOpen(token) => match token {
                    WS_BBO_1 => {
                        handle.send_message(WS_BBO_1, gate::generate_bbo_subscribe_request())?;
                    }
                    ORDER_TOKEN_1 => {
                        info!("order connection opened");
                        let req = gate::generate_login_request();
                        info!("login request: {}", req);
                        crate::flush_logs!();
                        handle.send_message(ORDER_TOKEN_1, gate::generate_login_request())?;
                    }
                    ORDER_TOKEN_2 => {
                        let req = gate::generate_order_sub_request();
                        info!("order sub request: {}", req);
                        crate::flush_logs!();
                        handle.send_message(ORDER_TOKEN_2, req)?;
                    }
                    _ => (),
                },
                CallbackData::ConnectionClose(token, err) => {
                    info!("connection close: {:?} {:?}", token, err);
                    flush_logs!();
                }
                CallbackData::ConnectionError(token, error) => {
                    error!("connection err: {:?}: {:?}", token, error);
                    flush_logs!();
                }
            }
            Ok(())
        };
    let mut settings = Settings::default();
    settings.event_loop_timeout = Some(std::time::Duration::from_millis(100));
    let mut websocket = WebSocket::new(settings, callback)?;

    let gate_bbo_url: Url = generate_bbo_url().into();
    info!("sbe bbo url: {}", gate_bbo_url);
    websocket.connect(gate_bbo_url.clone(), WS_BBO_1)?;

    let bn_bbo_url: Url = generate_futures_book_ticker_url(GATE_BASE_ASSETS).into();
    info!("bbo url: {}", bn_bbo_url);
    websocket.connect(bn_bbo_url.clone(), WS_BBO_2)?;

    let gate_order_url: Url = generate_bbo_url().into();
    info!("order url: {}", gate_order_url);
    websocket.connect(gate_order_url.clone(), ORDER_TOKEN_1)?;
    websocket.connect(gate_order_url.clone(), ORDER_TOKEN_2)?;

    match websocket.run() {
        Ok(_) => (),
        Err(e) => {
            libwebsocket_rs::error!("Websocket run error: {:?}", e);
            libwebsocket_rs::flush_logs!();
        }
    }
    Ok(())
}
